import { Component } from "@angular/core";
import { CommonModule } from "@angular/common";
import { IonContent } from "@ionic/angular/standalone";
import { HeaderComponent } from "../../components/header/header";
import { TableComponent } from "../../components/table/table";
import { BillingComponent } from "../../components/billing/billing";
import { DataService } from "src/app/services/data";
import { CommonService } from "src/app/services/common";
import { TypeSenseService } from "src/app/services/typesense";
@Component({
    selector: 'app-invoices',
    standalone: true,
    templateUrl: './invoices.html',
    imports: [CommonModule, IonContent, HeaderComponent, TableComponent, BillingComponent]
})
export class InvoicesComponent {
    invoices: any[] = [];
    invoicesColumns: any[] = [];
    cartItems: any[] = [];
    isLoading = false;
    errorMessage = '';
    ordersData: any[] = [];

    constructor(
        private data: DataService,
        private commonService: CommonService,
        private typesenseService: TypeSenseService
    ) {
        this.invoicesColumns = [
            { field: 'order_id', header: 'Order ID' },
            { field: 'customer_name', header: 'Customer' },
            { field: 'created_at', header: 'Date' },
            { field: 'status', header: 'Status' },
            { field: 'total_amount', header: 'Total' },
        ];
        this.loadOrders();
    }
    loadOrders() {
        this.isLoading = true;
        this.errorMessage = '';

        // Set auth token for API call (same as in checkout)
        localStorage.setItem('auth_token', 'test-token');

        this.commonService.get('get_all_orders').subscribe({
            next: (response) => {
                console.log('Orders fetched successfully:', response);
                console.log('Response type:', typeof response);
                console.log('Response keys:', Object.keys(response || {}));

                // Handle different possible response structures
                let ordersArray: any[] = [];

                if (Array.isArray(response)) {
                    // If response is directly an array
                    ordersArray = response;
                    console.log('Response is direct array, length:', ordersArray.length);
                } else if (response && (response as any).orders) {
                    // If response has orders property
                    ordersArray = (response as any).orders;
                    console.log('Response has orders property, length:', ordersArray.length);
                } else if (response && Array.isArray((response as any).data)) {
                    // If response has data property with array
                    ordersArray = (response as any).data;
                    console.log('Response has data property, length:', ordersArray.length);
                } else {
                    console.log('Unknown response structure:', response);
                }

                console.log('Orders array before mapping:', ordersArray);

                this.invoices = ordersArray.map((order: any) => {
                    console.log('Processing order:', order);

                    // Ensure items exist and is an array
                    const items = Array.isArray(order.items) ? order.items : [];
                    console.log('Order items:', items);

                    return {
                        ...order,
                        items: items.map((item: any) => ({
                            ...item,
                            name: item.name,
                            sku : item.sku,
                            thumbnail_url: item.thumbnail_url,
                            price: item.sale_price || item.price || 0,
                            quantity: item.quantity || 1
                        }))
                    };
                });

                console.log('Final mapped orders:', this.invoices);

                // Set first order's items as default selection if available
                if (this.invoices.length > 0) {
                    this.ordersData = this.invoices[0];
                    this.cartItems = (this.ordersData as any).items || [];
                    console.log('Selected order data:', this.ordersData);
                    console.log('Cart items:', this.cartItems);
                }
                this.isLoading = false;
            },
            error: (error) => {
                console.error('Error fetching orders:', error);
                this.errorMessage = 'Failed to load orders. Please try again.';
                this.isLoading = false;

               
                
            }
        });
    }

    // This method creates mock data when API fails
    // We use a loop to generate multiple sample orders to simulate real data
    // Each order needs unique details like ID, customer, date etc.
    // prepareInvoices(count: number = 20) {
    //     this.invoices = [];
    //     for (let i = 0; i < count; i++) {
    //         const items = this.addDefaultItemsToCart();
    //         const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            
    //         this.invoices.push({
    //             order_id: `ORD-${i}`,
    //             customer_name: `Customer ${i}`,
    //             created_at: new Date(Date.now()).toISOString(),
    //             status: '',
    //             total_amount: total,
    //             items: items
    //         });
    //     }
    //     this.cartItems = this.invoices[0].items;
    // }
    // addDefaultItemsToCart(){
    //     const allProducts = this.data.allProducts;
    //     const items: any[] = [];
    //     const itemsToAdd = Math.floor(Math.random() * 10) + 1;
    //     const availableProducts = [...allProducts];
    //     for (let i = 0; i < itemsToAdd; i++) {
    //         const randomIndex = Math.floor(Math.random() * availableProducts.length);
    //         const randomProduct = availableProducts[randomIndex];
    //         const quantity = Math.floor(Math.random() * 5) + 1;
    //         const checkExist = items.find(item => item.id === randomProduct.id);
    //         if(!checkExist){
    //             items.push({...randomProduct, quantity: quantity});
    //         }
    //         availableProducts.splice(randomIndex, 1);
    //         if (availableProducts.length === 0) {
    //             break;
    //         }
    //     }
    //     return items;
    // }
    onSelect(event: any){
        // Handle both real API data and mock data structure
        this.ordersData = event?.event?.data || [];
        this.cartItems = Array.isArray(this.ordersData) ? this.ordersData[0]?.items || [] : (this.ordersData as any).items || [];
    }
}