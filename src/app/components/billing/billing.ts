import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
  ChangeDetectorRef,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IftaLabelModule } from 'primeng/iftalabel';
import { InputTextModule } from 'primeng/inputtext';
import { NoDataComponent } from '../no-data/no-data';
import { ProductComponent } from '../product/product';
import { TableModule } from 'primeng/table';
import { InputNumberModule } from 'primeng/inputnumber';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { CommonService } from '../../services/common';
import { TypeSenseService } from '../../services/typesense';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@Component({
  selector: 'app-billing',
  standalone: true,
  templateUrl: './billing.html',
  imports: [
    CommonModule,
    FormsModule,
    IftaLabelModule,
    InputTextModule,
    NoDataComponent,
    ProductComponent,
    TableModule,
    InputNumberModule,
    DividerModule,
    ButtonModule,
    AutoCompleteModule,
    ProgressSpinnerModule,
  ],
})
export class BillingComponent implements OnChanges {
  @Input() cartItems: any[] = [];
  @Input() quantityEdit = false;
  @Input() showActions = false;
  @Output() searchEvent = new EventEmitter<{
    searchText: string;
    event: any;
    keyCode: number;
  }>();
  searchText: string = '';
  selectedProduct: any = null;
  products: any[] = [];
  filteredProducts: any[] = [];
  selectedIndex: number = 0;


  // Checkout properties
  isProcessingCheckout = false;

  @ViewChild('searchInput') searchInput: any;
  constructor(
    private commonService: CommonService,
    private typesenseService: TypeSenseService,
    private cdr: ChangeDetectorRef,
  ) {}
  ngOnChanges(_changes: SimpleChanges): void {
    console.log(this.cartItems);
  }

  addToCart(product: any) {
    console.log('=== ADDING PRODUCT TO CART ===');
    console.log('Product being added:', product);
    console.log('Product keys:', Object.keys(product));
    console.log('Product name:', product.name);
    console.log('Product image:', product.image);
    console.log('Product thumbnail_url:', product.thumbnail_url);
    console.log('Product sku:', product.sku);

    const cartItem = this.cartItems?.find(
      (item: any) => item.id === product.id,
    );
    if (cartItem) {
      cartItem.quantity += 1;
      console.log('Updated existing cart item quantity:', cartItem.quantity);
    } else {
      const newCartItem = { ...product, quantity: 1 };
      console.log('New cart item being added:', newCartItem);
      this.cartItems?.push(newCartItem);
      this.cdr.detectChanges();
    }

    console.log('Cart items after addition:', this.cartItems);

    // Clear the search and selection after adding to cart
    this.products = [];
    this.selectedProduct = null;
    this.filteredProducts = [];
  }

  onSearchChange(event: any) {
    const query = event.query;
    if (query && query.length > 2) {
      this.getsearchProducts(query);
    } else {
      this.filteredProducts = [];
    }
  }
  getsearchProducts(query: string) {
    this.typesenseService.searchProducts(query).then((result) => {
      this.filteredProducts = result.products || [];
      console.log('Search results for query:', query);
      console.log('Filtered products:', this.filteredProducts);
      if (this.filteredProducts.length > 0) {
        console.log('First product structure:', this.filteredProducts[0]);
        console.log('First product keys:', Object.keys(this.filteredProducts[0]));
      }
    });
  }
  removeFromCart(product: any) {
    const index = this.cartItems.findIndex((item) => item.id === product.id);
    if (index > -1) {
      this.cartItems.splice(index, 1);
    }
  }
  updateQuantity(event: any, product: any) {
    const newQuantity = event.value;

    if (newQuantity < 1) {
      // Remove item from cart if quantity is less than 1
      this.removeFromCart(product);
    } else {
      product.quantity = newQuantity;
    }
  }
  getSubTotal() {
    return this.cartItems.reduce(
      (total, item) => total + item.price * item.quantity,
      0,
    );
  }
  getDiscount() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) * 0.1
    );
  }
  getGrandTotal() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) - this.getDiscount()
    );
  }
  onSearch(event: any) {
    this.searchEvent.emit({
      searchText: event.target.value,
      event,
      keyCode: event.keyCode || 0,
    });
  }
  onClearSearch() {
    this.searchText = '';
  }
  clearCart() {
    this.cartItems = [];
  }

  async checkout() {
    if (this.cartItems.length === 0) {
      this.commonService.toast({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Cart is empty. Add items to checkout.',
      });
      return;
    }

    // Set auth token for API call
    localStorage.setItem('auth_token', 'test-token');

    this.isProcessingCheckout = true;

    try {
      // Debug: Check what products are available in TypeSense
      console.log('=== DEBUGGING TYPESENSE PRODUCTS ===');
      const allProducts = await this.typesenseService.debugGetAllProducts();
      console.log('Total products found in TypeSense:', allProducts.length);

      // Fetch complete product details for each cart item by SKU
      console.log('=== CART ITEMS BEFORE ENRICHMENT ===');
      console.log('Cart items:', this.cartItems);

      // Test search for first cart item SKU
      if (this.cartItems.length > 0 && this.cartItems[0].sku) {
        console.log('=== TESTING SEARCH FOR FIRST CART ITEM ===');
        console.log('Testing search for SKU:', this.cartItems[0].sku);
        const testResult = await this.typesenseService.getProductBySku(this.cartItems[0].sku);
        console.log('Test search result:', testResult);
      }

      const enrichedItems = await Promise.all(
        this.cartItems.map(async (item) => {
          console.log('Processing cart item:', item);
          console.log('Item SKU:', item.sku);

          // If item doesn't have SKU, use the item as-is with fallback data
          if (!item.sku) {
            console.warn('Cart item missing SKU, using item data directly:', item);
            return {
              sku: item.id || `FALLBACK-${Date.now()}`, // Generate fallback SKU
              name: item.name || 'Unknown Product',
              description: item.description || '',
              image: item.image || item.thumbnail_url || '',
              thumbnail_url: item.thumbnail_url || item.image || '',
              category: item.category || '',
              categories: item.categories || [],
              brand: item.brand || '',
              tags: item.tags || [],
              quantity: item.quantity,
              unit_price: item.price,
              sale_price: item.price,
              mrp: item.mrp || item.price,
              weight: item.weight || '',
              dimensions: item.dimensions || '',
              availability: item.availability !== undefined ? item.availability : true,
              stock_quantity: item.stock_quantity || 0
            };
          }

          const productDetails = await this.typesenseService.getProductBySku(item.sku);

          if (productDetails) {
            console.log('Found product details for SKU:', item.sku, productDetails);
            // Merge cart item data with complete product details
            return {
              sku: item.sku,
              name: productDetails.name || item.name,
              description: productDetails.description || '',
              image: productDetails.image_url?.[0] || productDetails.thumbnail_url || '',
              thumbnail_url: productDetails.thumbnail_url || productDetails.image_url?.[0] || '',
              image_url: productDetails.image_url || [],
              category: productDetails.category || '',
              categories: productDetails.categories || [],
              subcategories: productDetails.subcategories || [],
              brand: productDetails.brand || '',
              tags: productDetails.tags || [],
              colour: productDetails.colour || '',
              weight: productDetails.weight || '',
              weight_unit: productDetails.weight_unit || '',
              facility_id: productDetails.facility_id || '',
              facility_name: productDetails.facility_name || '',
              is_active: productDetails.is_active !== undefined ? productDetails.is_active : true,
              display_alias: productDetails.display_alias || [],
              quantity: item.quantity,
              unit_price: item.unit_price || item.price || productDetails.price,
              sale_price: item.sale_price || item.price || productDetails.price,
              price: item.price || productDetails.price,
              mrp: productDetails.mrp || productDetails.price || item.price,
              availability: productDetails.availability !== undefined ? productDetails.availability : true,
              stock_quantity: productDetails.stock_quantity || 0,
              created_at: productDetails.created_at || '',
              updated_at: productDetails.updated_at || ''
            };
          } else {
            // Fallback to cart item data if product not found
            console.warn(`Product with SKU ${item.sku} not found in database, using cart item data`);
            return {
              sku: item.sku,
              name: item.name || 'Unknown Product',
              description: item.description || '',
              image: item.image || item.thumbnail_url || '',
              thumbnail_url: item.thumbnail_url || item.image || '',
              image_url: item.image_url || [],
              category: item.category || '',
              categories: item.categories || [],
              subcategories: item.subcategories || [],
              brand: item.brand || '',
              tags: item.tags || [],
              colour: item.colour || '',
              weight: item.weight || '',
              weight_unit: item.weight_unit || '',
              facility_id: item.facility_id || '',
              facility_name: item.facility_name || '',
              is_active: item.is_active !== undefined ? item.is_active : true,
              display_alias: item.display_alias || [],
              quantity: item.quantity,
              unit_price: item.unit_price || item.price,
              sale_price: item.sale_price || item.price,
              price: item.price,
              mrp: item.mrp || item.price,
              availability: item.availability !== undefined ? item.availability : true,
              stock_quantity: item.stock_quantity || 0,
              created_at: item.created_at || '',
              updated_at: item.updated_at || ''
            };
          }
        })
      );

      console.log('=== ENRICHED ITEMS BEING SENT TO BACKEND ===');
      console.log('Enriched items:', enrichedItems);
      console.log('First enriched item:', enrichedItems[0]);
      console.log('First enriched item keys:', Object.keys(enrichedItems[0] || {}));

      // Create order payload with enriched product data
      const totalAmount = this.getGrandTotal();
      const orderData = {
        customer_id: 'CUST-001',
        customer_name: 'Test User',
        facility_id: 'FAC-001',
        facility_name: 'Test Facility',
        total_amount: totalAmount,
        items: enrichedItems
      };

      console.log('=== ORDER DATA BEING SENT TO BACKEND ===');
      console.log('Order data:', orderData);
      console.log('Order items:', orderData.items);

      this.commonService.post('create_order', orderData).subscribe({
        next: (response) => {
          console.log('Order created successfully:', response);
          this.commonService.toast({
            severity: 'success',
            summary: 'Success',
            detail: `Order created successfully! Total: ₹${totalAmount.toFixed(2)}`
          });
          this.clearCart();
          this.isProcessingCheckout = false;
        },
        error: (error) => {
          console.error('Order creation error:', error);
          let errorMessage = 'Order creation failed. Please try again.';

          if (error.status === 401) {
            errorMessage = 'Authentication failed. Please check your token.';
          } else if (error.status === 400) {
            errorMessage = 'Invalid order data. Please check the items.';
          }

          this.commonService.toast({
            severity: 'error',
            summary: 'Error',
            detail: errorMessage
          });
          this.isProcessingCheckout = false;
        }
      });
    } catch (error) {
      console.error('Error enriching cart items:', error);
      this.commonService.toast({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to fetch product details. Please try again.'
      });
      this.isProcessingCheckout = false;
    }
  }


}
