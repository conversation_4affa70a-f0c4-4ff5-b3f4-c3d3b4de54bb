import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  Output,
  OnChanges,
  SimpleChanges,
  ChangeDetectorRef,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IftaLabelModule } from 'primeng/iftalabel';
import { InputTextModule } from 'primeng/inputtext';
import { NoDataComponent } from '../no-data/no-data';
import { ProductComponent } from '../product/product';
import { TableModule } from 'primeng/table';
import { InputNumberModule } from 'primeng/inputnumber';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { CommonService } from '../../services/common';
import { TypeSenseService } from '../../services/typesense';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@Component({
  selector: 'app-billing',
  standalone: true,
  templateUrl: './billing.html',
  imports: [
    CommonModule,
    FormsModule,
    IftaLabelModule,
    InputTextModule,
    NoDataComponent,
    ProductComponent,
    TableModule,
    InputNumberModule,
    DividerModule,
    ButtonModule,
    AutoCompleteModule,
    ProgressSpinnerModule,
  ],
})
export class BillingComponent implements OnChanges {
  @Input() cartItems: any[] = [];
  @Input() quantityEdit = false;
  @Input() showActions = false;
  @Output() searchEvent = new EventEmitter<{
    searchText: string;
    event: any;
    keyCode: number;
  }>();
  searchText: string = '';
  selectedProduct: any = null;
  products: any[] = [];
  filteredProducts: any[] = [];
  selectedIndex: number = 0;


  // Checkout properties
  isProcessingCheckout = false;

  @ViewChild('searchInput') searchInput: any;
  constructor(
    private commonService: CommonService,
    private typesenseService: TypeSenseService,
    private cdr: ChangeDetectorRef,
  ) {}
  ngOnChanges(_changes: SimpleChanges): void {
    console.log(this.cartItems);
  }

  addToCart(product: any) {
    const cartItem = this.cartItems?.find(
      (item: any) => item.id === product.id,
    );
    if (cartItem) {
      cartItem.quantity += 1;
    } else {
      this.cartItems?.push({ ...product, quantity: 1 });
      this.cdr.detectChanges();
    }
    // Clear the search and selection after adding to cart
    this.products = [];
    this.selectedProduct = null;
    this.filteredProducts = [];
  }

  onSearchChange(event: any) {
    const query = event.query;
    if (query && query.length > 2) {
      this.getsearchProducts(query);
    } else {
      this.filteredProducts = [];
    }
  }
  getsearchProducts(query: string) {
    this.typesenseService.searchProducts(query).then((result) => {
      this.filteredProducts = result.products || [];
    });
  }
  removeFromCart(product: any) {
    const index = this.cartItems.findIndex((item) => item.id === product.id);
    if (index > -1) {
      this.cartItems.splice(index, 1);
    }
  }
  updateQuantity(event: any, product: any) {
    const newQuantity = event.value;

    if (newQuantity < 1) {
      // Remove item from cart if quantity is less than 1
      this.removeFromCart(product);
    } else {
      product.quantity = newQuantity;
    }
  }
  getSubTotal() {
    return this.cartItems.reduce(
      (total, item) => total + item.price * item.quantity,
      0,
    );
  }
  getDiscount() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) * 0.1
    );
  }
  getGrandTotal() {
    return (
      this.cartItems.reduce(
        (total, item) => total + item.price * item.quantity,
        0,
      ) - this.getDiscount()
    );
  }
  onSearch(event: any) {
    this.searchEvent.emit({
      searchText: event.target.value,
      event,
      keyCode: event.keyCode || 0,
    });
  }
  onClearSearch() {
    this.searchText = '';
  }
  clearCart() {
    this.cartItems = [];
  }

  async checkout() {
    if (this.cartItems.length === 0) {
      this.commonService.toast({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Cart is empty. Add items to checkout.',
      });
      return;
    }

    // Set auth token for API call
    localStorage.setItem('auth_token', 'test-token');

    this.isProcessingCheckout = true;

    try {
      // Fetch complete product details for each cart item by SKU
      console.log('Cart items before enrichment:', this.cartItems);

      const enrichedItems = await Promise.all(
        this.cartItems.map(async (item) => {
          console.log('Processing cart item:', item);
          console.log('Item SKU:', item.sku);

          // If item doesn't have SKU, use the item as-is with fallback data
          if (!item.sku) {
            console.warn('Cart item missing SKU, using item data directly:', item);
            return {
              sku: item.id || `FALLBACK-${Date.now()}`, // Generate fallback SKU
              name: item.name || 'Unknown Product',
              description: item.description || '',
              image: item.image || item.thumbnail_url || '',
              thumbnail_url: item.thumbnail_url || item.image || '',
              category: item.category || '',
              categories: item.categories || [],
              brand: item.brand || '',
              tags: item.tags || [],
              quantity: item.quantity,
              unit_price: item.price,
              sale_price: item.price,
              mrp: item.mrp || item.price,
              weight: item.weight || '',
              dimensions: item.dimensions || '',
              availability: item.availability !== undefined ? item.availability : true,
              stock_quantity: item.stock_quantity || 0
            };
          }

          const productDetails = await this.typesenseService.getProductBySku(item.sku);

          if (productDetails) {
            console.log('Found product details for SKU:', item.sku, productDetails);
            // Merge cart item data with complete product details
            return {
              sku: item.sku,
              name: productDetails.name || item.name,
              description: productDetails.description || '',
              image: productDetails.image || productDetails.thumbnail_url || '',
              thumbnail_url: productDetails.thumbnail_url || productDetails.image || '',
              category: productDetails.category || '',
              categories: productDetails.categories || [],
              brand: productDetails.brand || '',
              tags: productDetails.tags || [],
              quantity: item.quantity,
              unit_price: item.price,
              sale_price: item.price,
              mrp: productDetails.mrp || productDetails.price || item.price,
              weight: productDetails.weight || '',
              dimensions: productDetails.dimensions || '',
              availability: productDetails.availability || true,
              stock_quantity: productDetails.stock_quantity || 0
            };
          } else {
            // Fallback to cart item data if product not found
            console.warn(`Product with SKU ${item.sku} not found in database, using cart item data`);
            return {
              sku: item.sku,
              name: item.name || 'Unknown Product',
              description: item.description || '',
              image: item.image || item.thumbnail_url || '',
              thumbnail_url: item.thumbnail_url || item.image || '',
              category: item.category || '',
              categories: item.categories || [],
              brand: item.brand || '',
              tags: item.tags || [],
              quantity: item.quantity,
              unit_price: item.price,
              sale_price: item.price,
              mrp: item.mrp || item.price,
              weight: item.weight || '',
              dimensions: item.dimensions || '',
              availability: item.availability !== undefined ? item.availability : true,
              stock_quantity: item.stock_quantity || 0
            };
          }
        })
      );

      console.log('Enriched items:', enrichedItems);

      // Create order payload with enriched product data
      const totalAmount = this.getGrandTotal();
      const orderData = {
        customer_id: 'CUST-001',
        customer_name: 'Test User',
        facility_id: 'FAC-001',
        facility_name: 'Test Facility',
        total_amount: totalAmount,
        items: enrichedItems
      };

      this.commonService.post('create_order', orderData).subscribe({
        next: (response) => {
          console.log('Order created successfully:', response);
          this.commonService.toast({
            severity: 'success',
            summary: 'Success',
            detail: `Order created successfully! Total: ₹${totalAmount.toFixed(2)}`
          });
          this.clearCart();
          this.isProcessingCheckout = false;
        },
        error: (error) => {
          console.error('Order creation error:', error);
          let errorMessage = 'Order creation failed. Please try again.';

          if (error.status === 401) {
            errorMessage = 'Authentication failed. Please check your token.';
          } else if (error.status === 400) {
            errorMessage = 'Invalid order data. Please check the items.';
          }

          this.commonService.toast({
            severity: 'error',
            summary: 'Error',
            detail: errorMessage
          });
          this.isProcessingCheckout = false;
        }
      });
    } catch (error) {
      console.error('Error enriching cart items:', error);
      this.commonService.toast({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to fetch product details. Please try again.'
      });
      this.isProcessingCheckout = false;
    }
  }


}
